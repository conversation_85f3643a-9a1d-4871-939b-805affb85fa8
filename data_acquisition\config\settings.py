"""
Configuration settings for the A-Share Data Acquisition Framework
"""

import os
from pathlib import Path
from typing import Dict, List, Optional

class Config:
    """Main configuration class for the data acquisition framework"""
    
    # Base directories
    BASE_DIR = Path(__file__).parent.parent.parent
    DATA_DIR = BASE_DIR / "data"
    CACHE_DIR = DATA_DIR / "cache"
    LOG_DIR = BASE_DIR / "logs"
    
    # Database settings
    DATABASE_URL = os.getenv('DATABASE_URL', f'sqlite:///{DATA_DIR}/ashare_data.db')
    DATABASE_POOL_SIZE = int(os.getenv('DATABASE_POOL_SIZE', '10'))
    DATABASE_TIMEOUT = int(os.getenv('DATABASE_TIMEOUT', '30'))
    
    # Cache settings
    CACHE_ENABLED = os.getenv('CACHE_ENABLED', 'true').lower() == 'true'
    CACHE_TTL_DAYS = int(os.getenv('CACHE_TTL_DAYS', '7'))  # Cache validity in days
    CACHE_MAX_SIZE_MB = int(os.getenv('CACHE_MAX_SIZE_MB', '1000'))  # Max cache size
    
    # API Rate limiting
    AKSHARE_RATE_LIMIT = float(os.getenv('AKSHARE_RATE_LIMIT', '0.5'))  # Seconds between requests
    WEB_SCRAPER_RATE_LIMIT = float(os.getenv('WEB_SCRAPER_RATE_LIMIT', '1.0'))
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))
    RETRY_DELAY = float(os.getenv('RETRY_DELAY', '2.0'))
    
    # Data validation settings
    VALIDATE_DATA = os.getenv('VALIDATE_DATA', 'true').lower() == 'true'
    MIN_DATA_POINTS = int(os.getenv('MIN_DATA_POINTS', '10'))  # Minimum data points for validation
    MAX_PRICE_CHANGE_PERCENT = float(os.getenv('MAX_PRICE_CHANGE_PERCENT', '50.0'))  # Max daily price change
    
    # Logging settings
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE_MAX_SIZE = int(os.getenv('LOG_FILE_MAX_SIZE', '10485760'))  # 10MB
    LOG_BACKUP_COUNT = int(os.getenv('LOG_BACKUP_COUNT', '5'))
    
    # Stock market settings
    TRADING_DAYS_PER_YEAR = 250
    MARKET_OPEN_TIME = "09:30"
    MARKET_CLOSE_TIME = "15:00"
    
    # Supported exchanges
    SUPPORTED_EXCHANGES = ['SZ', 'SH']  # Shenzhen, Shanghai
    
    # Data types to collect
    DEFAULT_DATA_TYPES = [
        'daily_price',      # OHLCV data
        'volume',           # Trading volume
        'market_cap',       # Market capitalization
        'financial_data',   # Financial statements
        'index_data'        # Market indices
    ]
    
    # Web scraping settings
    WEB_SCRAPER_TIMEOUT = int(os.getenv('WEB_SCRAPER_TIMEOUT', '30'))
    WEB_SCRAPER_USER_AGENT = os.getenv(
        'WEB_SCRAPER_USER_AGENT', 
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    )
    
    # Fallback data sources
    FALLBACK_SOURCES = [
        'tencent',
        'eastmoney'
    ]
    
    # Data update settings
    AUTO_UPDATE_ENABLED = os.getenv('AUTO_UPDATE_ENABLED', 'true').lower() == 'true'
    UPDATE_FREQUENCY_HOURS = int(os.getenv('UPDATE_FREQUENCY_HOURS', '24'))
    
    @classmethod
    def create_directories(cls):
        """Create necessary directories if they don't exist"""
        for directory in [cls.DATA_DIR, cls.CACHE_DIR, cls.LOG_DIR]:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def get_database_config(cls) -> Dict:
        """Get database configuration dictionary"""
        return {
            'url': cls.DATABASE_URL,
            'pool_size': cls.DATABASE_POOL_SIZE,
            'timeout': cls.DATABASE_TIMEOUT
        }
    
    @classmethod
    def get_cache_config(cls) -> Dict:
        """Get cache configuration dictionary"""
        return {
            'enabled': cls.CACHE_ENABLED,
            'ttl_days': cls.CACHE_TTL_DAYS,
            'max_size_mb': cls.CACHE_MAX_SIZE_MB,
            'cache_dir': cls.CACHE_DIR
        }
    
    @classmethod
    def get_rate_limit_config(cls) -> Dict:
        """Get rate limiting configuration"""
        return {
            'akshare_limit': cls.AKSHARE_RATE_LIMIT,
            'web_scraper_limit': cls.WEB_SCRAPER_RATE_LIMIT,
            'max_retries': cls.MAX_RETRIES,
            'retry_delay': cls.RETRY_DELAY
        }
    
    @classmethod
    def get_validation_config(cls) -> Dict:
        """Get data validation configuration"""
        return {
            'enabled': cls.VALIDATE_DATA,
            'min_data_points': cls.MIN_DATA_POINTS,
            'max_price_change_percent': cls.MAX_PRICE_CHANGE_PERCENT
        }

# Development/Testing configuration
class DevConfig(Config):
    """Development configuration with more verbose logging and smaller limits"""
    LOG_LEVEL = 'DEBUG'
    CACHE_TTL_DAYS = 1
    MAX_RETRIES = 2
    AKSHARE_RATE_LIMIT = 0.1  # Faster for testing

# Production configuration  
class ProdConfig(Config):
    """Production configuration with optimized settings"""
    LOG_LEVEL = 'WARNING'
    CACHE_TTL_DAYS = 30
    MAX_RETRIES = 5
    AKSHARE_RATE_LIMIT = 1.0  # More conservative

# Configuration factory
def get_config(env: str = None) -> Config:
    """
    Get configuration based on environment
    
    Args:
        env: Environment name ('dev', 'prod', or None for default)
        
    Returns:
        Config: Configuration instance
    """
    env = env or os.getenv('ENVIRONMENT', 'default')
    
    if env.lower() == 'dev':
        return DevConfig()
    elif env.lower() == 'prod':
        return ProdConfig()
    else:
        return Config()
