"""
A-Share Data Acquisition Framework

A comprehensive framework for acquiring Chinese stock market data for backtesting purposes.
Supports multiple data sources including akshare API and web scraping fallbacks.

Main Components:
- DataManager: Central coordinator for all data operations
- AkshareProvider: Primary data source using akshare library
- WebScrapers: Fallback data sources for missing data
- Storage: Local data storage and caching systems
- BacktestingInterface: Clean interface for backtesting systems

Usage:
    from data_acquisition import DataManager
    
    # Initialize data manager
    dm = DataManager()
    
    # Get stock data
    data = dm.get_stock_data('000001.SZ', start_date='2020-01-01', end_date='2023-12-31')
    
    # Get multiple stocks
    stocks = ['000001.SZ', '000002.SZ', '600000.SH']
    data = dm.get_multiple_stocks_data(stocks, start_date='2020-01-01')
"""

from .core.data_manager import DataManager
from .core.akshare_provider import AkshareProvider
from .core.web_scraper import WebScraper
from .backtesting_interface.data_interface import BacktestingDataInterface
from .utils.stock_codes import StockCodeValidator, normalize_stock_code
from .config.settings import Config

__version__ = "1.0.0"
__author__ = "A-Share Backtesting Framework"

# Main exports
__all__ = [
    'DataManager',
    'AkshareProvider', 
    'WebScraper',
    'BacktestingDataInterface',
    'StockCodeValidator',
    'normalize_stock_code',
    'Config'
]

# Quick access functions
def get_data_manager(**kwargs):
    """
    Quick access to create a DataManager instance
    
    Args:
        **kwargs: Configuration parameters for DataManager
        
    Returns:
        DataManager: Configured data manager instance
    """
    return DataManager(**kwargs)

def get_backtesting_interface(**kwargs):
    """
    Quick access to create a BacktestingDataInterface instance
    
    Args:
        **kwargs: Configuration parameters for BacktestingDataInterface
        
    Returns:
        BacktestingDataInterface: Configured backtesting interface
    """
    return BacktestingDataInterface(**kwargs)
