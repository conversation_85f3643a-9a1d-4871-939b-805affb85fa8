"""
Data preprocessing utilities for backtesting
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime, date
from ..config.settings import Config
from ..utils.logger import get_logger

class DataPreprocessor:
    """Data preprocessing utilities for backtesting"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize data preprocessor
        
        Args:
            config: Configuration instance
        """
        self.config = config or Config()
        self.logger = get_logger('data_preprocessor', config)
    
    def align_data(self, data_dict: Dict[str, pd.DataFrame], 
                   method: str = 'outer') -> Dict[str, pd.DataFrame]:
        """
        Align multiple DataFrames to the same date index
        
        Args:
            data_dict: Dictionary of DataFrames
            method: Alignment method ('outer', 'inner')
            
        Returns:
            Dict[str, pd.DataFrame]: Aligned DataFrames
        """
        try:
            if not data_dict:
                return {}
            
            # Get all unique dates
            all_dates = set()
            for data in data_dict.values():
                if not data.empty:
                    all_dates.update(data.index)
            
            if not all_dates:
                return data_dict
            
            # Create common date index
            common_index = pd.DatetimeIndex(sorted(all_dates))
            
            # Align all DataFrames
            aligned_data = {}
            for key, data in data_dict.items():
                if data.empty:
                    aligned_data[key] = pd.DataFrame(index=common_index)
                else:
                    if method == 'outer':
                        aligned_data[key] = data.reindex(common_index)
                    elif method == 'inner':
                        # Only keep dates that exist in all DataFrames
                        common_dates = common_index
                        for other_data in data_dict.values():
                            if not other_data.empty:
                                common_dates = common_dates.intersection(other_data.index)
                        aligned_data[key] = data.reindex(common_dates)
            
            return aligned_data
            
        except Exception as e:
            self.logger.error(f"Error aligning data: {e}")
            return data_dict
    
    def fill_missing_data(self, data: pd.DataFrame, 
                         method: str = 'forward',
                         limit: Optional[int] = None) -> pd.DataFrame:
        """
        Fill missing data in DataFrame
        
        Args:
            data: Input DataFrame
            method: Fill method ('forward', 'backward', 'interpolate', 'zero')
            limit: Maximum number of consecutive NaN values to fill
            
        Returns:
            pd.DataFrame: DataFrame with filled data
        """
        try:
            if data.empty:
                return data
            
            filled_data = data.copy()
            
            if method == 'forward':
                filled_data = filled_data.fillna(method='ffill', limit=limit)
            elif method == 'backward':
                filled_data = filled_data.fillna(method='bfill', limit=limit)
            elif method == 'interpolate':
                filled_data = filled_data.interpolate(method='linear', limit=limit)
            elif method == 'zero':
                filled_data = filled_data.fillna(0)
            else:
                self.logger.warning(f"Unknown fill method: {method}")
            
            return filled_data
            
        except Exception as e:
            self.logger.error(f"Error filling missing data: {e}")
            return data
    
    def calculate_returns(self, prices: pd.DataFrame, 
                         return_type: str = 'simple',
                         periods: int = 1) -> pd.DataFrame:
        """
        Calculate returns from price data
        
        Args:
            prices: Price DataFrame
            return_type: Type of returns ('simple', 'log')
            periods: Number of periods for return calculation
            
        Returns:
            pd.DataFrame: Returns DataFrame
        """
        try:
            if prices.empty:
                return pd.DataFrame()
            
            if return_type == 'simple':
                returns = prices.pct_change(periods=periods)
            elif return_type == 'log':
                returns = np.log(prices / prices.shift(periods))
            else:
                raise ValueError(f"Unknown return type: {return_type}")
            
            return returns
            
        except Exception as e:
            self.logger.error(f"Error calculating returns: {e}")
            return pd.DataFrame()
    
    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate common technical indicators
        
        Args:
            data: OHLCV data
            
        Returns:
            pd.DataFrame: Data with technical indicators
        """
        try:
            if data.empty or 'close' not in data.columns:
                return data
            
            result = data.copy()
            
            # Simple Moving Averages
            for window in [5, 10, 20, 50]:
                result[f'sma_{window}'] = result['close'].rolling(window=window).mean()
            
            # Exponential Moving Averages
            for span in [12, 26]:
                result[f'ema_{span}'] = result['close'].ewm(span=span).mean()
            
            # MACD
            if 'ema_12' in result.columns and 'ema_26' in result.columns:
                result['macd'] = result['ema_12'] - result['ema_26']
                result['macd_signal'] = result['macd'].ewm(span=9).mean()
                result['macd_histogram'] = result['macd'] - result['macd_signal']
            
            # RSI
            delta = result['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            result['rsi'] = 100 - (100 / (1 + rs))
            
            # Bollinger Bands
            sma_20 = result['close'].rolling(window=20).mean()
            std_20 = result['close'].rolling(window=20).std()
            result['bb_upper'] = sma_20 + (std_20 * 2)
            result['bb_lower'] = sma_20 - (std_20 * 2)
            result['bb_middle'] = sma_20
            
            # Volume indicators
            if 'volume' in result.columns:
                result['volume_sma_20'] = result['volume'].rolling(window=20).mean()
                result['volume_ratio'] = result['volume'] / result['volume_sma_20']
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error calculating technical indicators: {e}")
            return data
    
    def normalize_data(self, data: pd.DataFrame, 
                      method: str = 'zscore',
                      window: Optional[int] = None) -> pd.DataFrame:
        """
        Normalize data
        
        Args:
            data: Input data
            method: Normalization method ('zscore', 'minmax', 'robust')
            window: Rolling window for normalization (None for full period)
            
        Returns:
            pd.DataFrame: Normalized data
        """
        try:
            if data.empty:
                return data
            
            normalized_data = data.copy()
            
            for column in data.select_dtypes(include=[np.number]).columns:
                if method == 'zscore':
                    if window:
                        mean = data[column].rolling(window=window).mean()
                        std = data[column].rolling(window=window).std()
                        normalized_data[column] = (data[column] - mean) / std
                    else:
                        normalized_data[column] = (data[column] - data[column].mean()) / data[column].std()
                
                elif method == 'minmax':
                    if window:
                        min_val = data[column].rolling(window=window).min()
                        max_val = data[column].rolling(window=window).max()
                        normalized_data[column] = (data[column] - min_val) / (max_val - min_val)
                    else:
                        min_val = data[column].min()
                        max_val = data[column].max()
                        normalized_data[column] = (data[column] - min_val) / (max_val - min_val)
                
                elif method == 'robust':
                    if window:
                        median = data[column].rolling(window=window).median()
                        mad = data[column].rolling(window=window).apply(
                            lambda x: np.median(np.abs(x - np.median(x)))
                        )
                        normalized_data[column] = (data[column] - median) / mad
                    else:
                        median = data[column].median()
                        mad = np.median(np.abs(data[column] - median))
                        normalized_data[column] = (data[column] - median) / mad
            
            return normalized_data
            
        except Exception as e:
            self.logger.error(f"Error normalizing data: {e}")
            return data
    
    def resample_data(self, data: pd.DataFrame, 
                     frequency: str,
                     aggregation: Dict[str, str] = None) -> pd.DataFrame:
        """
        Resample data to different frequency
        
        Args:
            data: Input data with datetime index
            frequency: Target frequency ('W', 'M', 'Q', etc.)
            aggregation: Column-specific aggregation methods
            
        Returns:
            pd.DataFrame: Resampled data
        """
        try:
            if data.empty or not isinstance(data.index, pd.DatetimeIndex):
                return data
            
            # Default aggregation methods
            default_agg = {
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum',
                'amount': 'sum'
            }
            
            # Use provided aggregation or default
            agg_methods = aggregation or default_agg
            
            # Apply aggregation only to columns that exist
            available_agg = {col: method for col, method in agg_methods.items() 
                           if col in data.columns}
            
            if not available_agg:
                # If no specific aggregation, use mean for numeric columns
                resampled = data.resample(frequency).mean()
            else:
                resampled = data.resample(frequency).agg(available_agg)
            
            return resampled
            
        except Exception as e:
            self.logger.error(f"Error resampling data: {e}")
            return data
    
    def filter_outliers(self, data: pd.DataFrame, 
                       method: str = 'iqr',
                       threshold: float = 1.5) -> pd.DataFrame:
        """
        Filter outliers from data
        
        Args:
            data: Input data
            method: Outlier detection method ('iqr', 'zscore')
            threshold: Threshold for outlier detection
            
        Returns:
            pd.DataFrame: Data with outliers removed
        """
        try:
            if data.empty:
                return data
            
            filtered_data = data.copy()
            
            for column in data.select_dtypes(include=[np.number]).columns:
                if method == 'iqr':
                    Q1 = data[column].quantile(0.25)
                    Q3 = data[column].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - threshold * IQR
                    upper_bound = Q3 + threshold * IQR
                    
                    outlier_mask = (data[column] < lower_bound) | (data[column] > upper_bound)
                    filtered_data.loc[outlier_mask, column] = np.nan
                
                elif method == 'zscore':
                    z_scores = np.abs((data[column] - data[column].mean()) / data[column].std())
                    outlier_mask = z_scores > threshold
                    filtered_data.loc[outlier_mask, column] = np.nan
            
            return filtered_data
            
        except Exception as e:
            self.logger.error(f"Error filtering outliers: {e}")
            return data
    
    def create_features(self, data: pd.DataFrame, 
                       feature_config: Dict[str, Any] = None) -> pd.DataFrame:
        """
        Create features for backtesting
        
        Args:
            data: Input OHLCV data
            feature_config: Configuration for feature creation
            
        Returns:
            pd.DataFrame: Data with additional features
        """
        try:
            if data.empty:
                return data
            
            result = data.copy()
            
            # Default feature configuration
            default_config = {
                'returns': True,
                'technical_indicators': True,
                'price_features': True,
                'volume_features': True
            }
            
            config = feature_config or default_config
            
            # Price-based features
            if config.get('price_features', False) and 'close' in result.columns:
                # Price changes
                result['price_change'] = result['close'].diff()
                result['price_change_pct'] = result['close'].pct_change()
                
                # High-low spread
                if 'high' in result.columns and 'low' in result.columns:
                    result['hl_spread'] = result['high'] - result['low']
                    result['hl_spread_pct'] = result['hl_spread'] / result['close']
            
            # Returns
            if config.get('returns', False):
                result = pd.concat([result, self.calculate_returns(result[['close']])], axis=1)
            
            # Technical indicators
            if config.get('technical_indicators', False):
                result = self.calculate_technical_indicators(result)
            
            # Volume features
            if config.get('volume_features', False) and 'volume' in result.columns:
                result['volume_change'] = result['volume'].pct_change()
                result['volume_ma_ratio'] = result['volume'] / result['volume'].rolling(20).mean()
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error creating features: {e}")
            return data
