"""
A股数据采集框架安装脚本

自动安装和配置A股数据采集框架
包括依赖安装、目录创建、配置文件生成等
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} 版本兼容")
    return True

def install_requirements():
    """安装必需的包"""
    print("\n安装必需的包...")
    
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ requirements.txt 文件未找到")
        return False
    
    try:
        # 安装包
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ 所有包安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 包安装失败: {e}")
        print("您可能需要手动安装包:")
        print("pip install pandas numpy akshare requests beautifulsoup4")
        return False

def create_directories():
    """创建必要的目录"""
    print("\n创建目录...")
    
    directories = [
        "data",
        "data/cache", 
        "logs",
        "results",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")
    
    return True

def test_framework():
    """测试框架"""
    print("\n测试框架...")
    
    try:
        # 导入测试模块
        sys.path.insert(0, '.')
        import test_framework_cn
        
        # 运行测试
        success = test_framework_cn.run_all_tests()
        
        if success:
            print("✅ 框架测试通过")
        else:
            print("❌ 框架测试失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

def create_sample_config():
    """创建示例配置文件"""
    print("\n创建示例配置...")
    
    config_content = """# A股数据采集框架配置
# 复制此文件为 .env 并根据需要修改

# 数据库设置
DATABASE_URL=sqlite:///data/ashare_data.db

# 缓存设置
CACHE_ENABLED=true
CACHE_TTL_DAYS=7
CACHE_MAX_SIZE_MB=1000

# 限流设置（请求间隔秒数）
AKSHARE_RATE_LIMIT=0.5
WEB_SCRAPER_RATE_LIMIT=1.0
MAX_RETRIES=3
RETRY_DELAY=2.0

# 数据验证
VALIDATE_DATA=true
MIN_DATA_POINTS=10
MAX_PRICE_CHANGE_PERCENT=50.0

# 日志设置
LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# 网络爬虫设置
WEB_SCRAPER_TIMEOUT=30
WEB_SCRAPER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

# 环境 (dev, prod, 或 default)
ENVIRONMENT=default
"""
    
    config_file = Path("config.env.sample")
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print(f"✅ 示例配置文件已创建: {config_file}")
        print("   复制为 .env 文件并根据需要修改")
        return True
        
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False

def create_quick_start_script():
    """创建快速开始脚本"""
    print("\n创建快速开始脚本...")
    
    quick_start_content = '''"""
A股数据采集框架快速开始脚本
"""

from data_acquisition import DataManager
from datetime import datetime, timedelta

def quick_test():
    """快速测试框架功能"""
    print("A股数据采集框架快速测试")
    print("=" * 40)
    
    # 初始化数据管理器
    dm = DataManager()
    
    # 测试股票代码
    test_stocks = ["000001.SZ", "600000.SH", "600519.SH"]
    
    print(f"\\n测试股票: {test_stocks}")
    
    # 获取最近30天的数据
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    
    print(f"数据期间: {start_date} 到 {end_date}")
    
    for stock in test_stocks:
        try:
            print(f"\\n获取 {stock} 的数据...")
            data = dm.get_stock_data(stock, start_date, end_date)
            
            if data is not None and not data.empty:
                print(f"✅ 成功获取 {len(data)} 条记录")
                print(f"   最新收盘价: {data['close'].iloc[-1]:.2f}")
            else:
                print(f"❌ 无法获取数据")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    # 清理
    dm.cleanup()
    print(f"\\n测试完成！")

if __name__ == "__main__":
    quick_test()
'''
    
    script_file = Path("quick_start.py")
    
    try:
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(quick_start_content)
        
        print(f"✅ 快速开始脚本已创建: {script_file}")
        return True
        
    except Exception as e:
        print(f"❌ 创建快速开始脚本失败: {e}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "="*60)
    print("🎉 安装完成！")
    print("="*60)
    
    print("\n📖 快速开始:")
    print("-" * 30)
    
    print("\n1. 运行快速测试:")
    print("   python quick_start.py")
    
    print("\n2. 基础数据获取:")
    print("""
from data_acquisition import DataManager

dm = DataManager()
data = dm.get_stock_data('000001.SZ', '2023-01-01', '2023-12-31')
print(f"获取到 {len(data)} 条记录")
dm.cleanup()
""")
    
    print("\n3. 回测接口使用:")
    print("""
from data_acquisition import BacktestingDataInterface

bt = BacktestingDataInterface()
stocks = ['000001.SZ', '000002.SZ', '600000.SH']
price_matrix = bt.get_price_matrix(stocks, '2023-01-01', '2023-12-31')
bt.cleanup()
""")
    
    print("\n4. 运行示例:")
    print("   python examples/basic_usage_cn.py")
    print("   python examples/strategy_backtest_cn.py")
    
    print("\n📁 重要文件:")
    print("-" * 20)
    print("   README_CN.md              - 完整中文文档")
    print("   examples/                  - 使用示例")
    print("   test_framework_cn.py       - 测试框架")
    print("   config.env.sample          - 配置模板")
    print("   quick_start.py             - 快速开始")
    
    print("\n🔧 配置:")
    print("-" * 20)
    print("   1. 复制 config.env.sample 为 .env")
    print("   2. 根据需要修改设置")
    print("   3. 设置环境变量或使用 Config 类")
    
    print("\n📊 数据源:")
    print("-" * 20)
    print("   主要: akshare库 (安装: pip install akshare)")
    print("   备用: 网络爬虫 (腾讯财经、东方财富)")
    
    print("\n⚠️  重要提醒:")
    print("-" * 20)
    print("   - 遵守数据源使用条款")
    print("   - 合理使用限流设置")
    print("   - 启用缓存以减少API调用")
    print("   - 监控日志文件")
    
    print("\n🚀 下一步:")
    print("-" * 20)
    print("   1. 运行 python quick_start.py 测试")
    print("   2. 查看 examples/ 目录学习使用")
    print("   3. 阅读 README_CN.md 了解详情")
    print("   4. 开始您的量化投资之旅！")

def main():
    """主安装函数"""
    print("A股数据采集框架安装程序")
    print("=" * 50)
    
    steps = [
        ("检查Python版本", check_python_version),
        ("安装依赖包", install_requirements),
        ("创建目录", create_directories),
        ("创建示例配置", create_sample_config),
        ("创建快速开始脚本", create_quick_start_script),
        ("测试框架", test_framework)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        
        if not step_func():
            print(f"\n❌ 安装失败于: {step_name}")
            print("请检查上面的错误信息并重试。")
            return False
    
    show_usage_examples()
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
