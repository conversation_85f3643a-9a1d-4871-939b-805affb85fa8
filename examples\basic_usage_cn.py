"""
A股数据采集框架基础使用示例

本文件展示了如何使用A股数据采集框架进行基本的数据获取操作
包括单只股票、多只股票、缓存使用、数据验证等功能
"""

import pandas as pd
from datetime import datetime, date, timedelta
from data_acquisition import DataManager, BacktestingDataInterface
from data_acquisition.config import Config

def example_basic_data_acquisition():
    """示例：基础数据采集"""
    print("=== 基础数据采集示例 ===")
    
    # 初始化数据管理器
    dm = DataManager()
    
    # 获取单只股票数据
    stock_code = "000001.SZ"  # 平安银行
    start_date = "2023-01-01"
    end_date = "2023-12-31"
    
    print(f"正在获取 {stock_code} 从 {start_date} 到 {end_date} 的数据")
    
    data = dm.get_stock_data(stock_code, start_date, end_date)
    
    if data is not None:
        print(f"成功获取 {len(data)} 条记录")
        print("\n前5条记录:")
        print(data.head())
        print("\n数据列:", data.columns.tolist())
        
        # 显示基本统计信息
        print(f"\n数据统计:")
        print(f"  时间范围: {data.index.min()} 到 {data.index.max()}")
        print(f"  收盘价范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
        print(f"  平均成交量: {data['volume'].mean():.0f}")
    else:
        print("获取数据失败")
    
    # 清理资源
    dm.cleanup()

def example_multiple_stocks():
    """示例：获取多只股票数据"""
    print("\n=== 多只股票数据获取示例 ===")
    
    dm = DataManager()
    
    # 定义股票池
    stock_codes = [
        "000001.SZ",  # 平安银行
        "000002.SZ",  # 万科A
        "600000.SH",  # 浦发银行
        "600036.SH",  # 招商银行
        "600519.SH"   # 贵州茅台
    ]
    
    start_date = "2023-06-01"
    end_date = "2023-12-31"
    
    print(f"正在获取 {len(stock_codes)} 只股票的数据")
    
    all_data = dm.get_multiple_stocks_data(stock_codes, start_date, end_date)
    
    print(f"成功获取 {len(all_data)} 只股票的数据")
    
    # 显示每只股票的数据概况
    for stock_code, data in all_data.items():
        if data is not None:
            print(f"{stock_code}: {len(data)} 条记录, "
                  f"收盘价 {data['close'].iloc[-1]:.2f}")
        else:
            print(f"{stock_code}: 无数据")
    
    dm.cleanup()

def example_backtesting_interface():
    """示例：使用回测接口"""
    print("\n=== 回测接口使用示例 ===")
    
    # 初始化回测接口
    bt_interface = BacktestingDataInterface()
    
    # 定义参数
    stock_codes = ["000001.SZ", "000002.SZ", "600000.SH"]
    start_date = "2023-01-01"
    end_date = "2023-12-31"
    
    # 获取价格矩阵（用于投资组合回测）
    print("创建价格矩阵...")
    price_matrix = bt_interface.get_price_matrix(stock_codes, start_date, end_date, 'close')
    
    if not price_matrix.empty:
        print(f"价格矩阵形状: {price_matrix.shape}")
        print("\n前5行:")
        print(price_matrix.head())
        
        # 计算相关性
        correlation = price_matrix.corr()
        print(f"\n股票相关性矩阵:")
        print(correlation)
    
    # 获取收益率矩阵
    print("\n计算收益率矩阵...")
    returns_matrix = bt_interface.get_returns_matrix(stock_codes, start_date, end_date)
    
    if not returns_matrix.empty:
        print(f"收益率矩阵形状: {returns_matrix.shape}")
        print("\n收益率统计:")
        print(returns_matrix.describe())
    
    # 获取市场数据
    print("\n获取市场指数...")
    market_data = bt_interface.get_market_data(start_date, end_date)
    
    for index_code, data in market_data.items():
        if data is not None:
            print(f"{index_code}: {len(data)} 条记录")
    
    bt_interface.cleanup()

def example_data_validation():
    """示例：数据验证和清洗"""
    print("\n=== 数据验证示例 ===")
    
    dm = DataManager()
    
    # 获取一些数据
    stock_code = "000001.SZ"
    data = dm.get_stock_data(stock_code, "2023-01-01", "2023-12-31")
    
    if data is not None:
        # 验证数据
        validator = dm.validator
        is_valid, errors = validator.validate_price_data(data, stock_code)
        
        print(f"{stock_code} 数据验证结果:")
        print(f"有效性: {'通过' if is_valid else '失败'}")
        if errors:
            print("验证错误:")
            for error in errors:
                print(f"  - {error}")
        
        # 清洗数据
        cleaned_data = validator.clean_data(data, stock_code)
        print(f"原始数据: {len(data)} 条记录")
        print(f"清洗后数据: {len(cleaned_data)} 条记录")
        
        # 检查数据质量
        print(f"\n数据质量检查:")
        print(f"  缺失值: {data.isnull().sum().sum()}")
        print(f"  重复日期: {data.index.duplicated().sum()}")
        print(f"  异常价格变动: {((data['close'].pct_change().abs() > 0.2).sum())} 次")
    
    dm.cleanup()

def example_caching():
    """示例：缓存功能"""
    print("\n=== 缓存功能示例 ===")
    
    dm = DataManager()
    
    stock_code = "000001.SZ"
    start_date = "2023-01-01"
    end_date = "2023-12-31"
    
    # 第一次调用 - 将从数据源获取并缓存
    print("第一次调用（将缓存数据）...")
    start_time = datetime.now()
    data1 = dm.get_stock_data(stock_code, start_date, end_date, use_cache=True)
    time1 = (datetime.now() - start_time).total_seconds()
    print(f"耗时: {time1:.2f} 秒")
    
    # 第二次调用 - 将使用缓存数据
    print("第二次调用（将使用缓存）...")
    start_time = datetime.now()
    data2 = dm.get_stock_data(stock_code, start_date, end_date, use_cache=True)
    time2 = (datetime.now() - start_time).total_seconds()
    print(f"耗时: {time2:.2f} 秒")
    
    if time2 > 0:
        print(f"速度提升: {time1/time2:.1f}倍")
    
    # 检查缓存统计
    cache_stats = dm.cache.get_cache_stats()
    print("\n缓存统计:")
    for key, value in cache_stats.items():
        if key != 'data_type_breakdown':
            print(f"  {key}: {value}")
    
    dm.cleanup()

def example_configuration():
    """示例：自定义配置"""
    print("\n=== 配置示例 ===")
    
    # 创建自定义配置
    config = Config()
    
    # 修改设置
    config.CACHE_ENABLED = True
    config.CACHE_TTL_DAYS = 1  # 缓存仅1天
    config.AKSHARE_RATE_LIMIT = 0.1  # 测试时更快的限流
    
    print("自定义配置:")
    print(f"  缓存启用: {config.CACHE_ENABLED}")
    print(f"  缓存TTL: {config.CACHE_TTL_DAYS} 天")
    print(f"  akshare限流: {config.AKSHARE_RATE_LIMIT} 秒")
    
    # 使用自定义配置
    dm = DataManager(config)
    
    # 获取数据统计
    stats = dm.get_data_stats()
    print("\n数据管理器统计:")
    for key, value in stats.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for sub_key, sub_value in value.items():
                print(f"    {sub_key}: {sub_value}")
        else:
            print(f"  {key}: {value}")
    
    dm.cleanup()

def example_stock_info():
    """示例：获取股票信息"""
    print("\n=== 股票信息示例 ===")
    
    dm = DataManager()
    
    # 一些知名股票
    stock_codes = ["000001.SZ", "600519.SH", "000858.SZ"]
    stock_names = ["平安银行", "贵州茅台", "五粮液"]
    
    for stock_code, expected_name in zip(stock_codes, stock_names):
        print(f"\n获取 {stock_code} ({expected_name}) 的信息...")
        info = dm.get_stock_info(stock_code)
        
        if info:
            print(f"  名称: {info.get('name', '未知')}")
            print(f"  交易所: {info.get('exchange', '未知')}")
            print(f"  行业: {info.get('sector', '未知')}")
            print(f"  市值: {info.get('market_cap', '未知')}")
        else:
            print(f"  无法获取信息")
    
    dm.cleanup()

def example_market_analysis():
    """示例：市场分析"""
    print("\n=== 市场分析示例 ===")
    
    bt_interface = BacktestingDataInterface()
    
    # 不同板块的代表股票
    sectors = {
        '银行': ['000001.SZ', '600000.SH', '600036.SH'],
        '白酒': ['600519.SH', '000858.SZ'],
        '科技': ['000725.SZ', '002415.SZ']
    }
    
    start_date = "2023-01-01"
    end_date = "2023-12-31"
    
    sector_performance = {}
    
    for sector_name, stocks in sectors.items():
        print(f"\n分析 {sector_name} 板块...")
        
        # 获取板块数据
        sector_data = bt_interface.get_multiple_stocks_data(stocks, start_date, end_date)
        
        if sector_data:
            # 计算板块平均表现
            returns_list = []
            for stock, data in sector_data.items():
                if data is not None and not data.empty:
                    returns = data['close'].pct_change()
                    returns_list.append(returns)
            
            if returns_list:
                # 合并收益率
                sector_returns_df = pd.concat(returns_list, axis=1)
                sector_avg_returns = sector_returns_df.mean(axis=1)
                
                # 计算累计收益
                cumulative_returns = (1 + sector_avg_returns).cumprod()
                total_return = cumulative_returns.iloc[-1] - 1
                
                sector_performance[sector_name] = {
                    'total_return': total_return,
                    'volatility': sector_avg_returns.std() * (252**0.5),  # 年化波动率
                    'stocks_count': len([d for d in sector_data.values() if d is not None])
                }
                
                print(f"  总收益率: {total_return:.2%}")
                print(f"  年化波动率: {sector_performance[sector_name]['volatility']:.2%}")
                print(f"  有效股票数: {sector_performance[sector_name]['stocks_count']}")
    
    # 板块比较
    if sector_performance:
        print(f"\n板块表现排名:")
        sorted_sectors = sorted(sector_performance.items(), 
                              key=lambda x: x[1]['total_return'], reverse=True)
        
        for i, (sector, perf) in enumerate(sorted_sectors, 1):
            print(f"  {i}. {sector}: {perf['total_return']:.2%}")
    
    bt_interface.cleanup()

if __name__ == "__main__":
    """运行所有示例"""
    
    print("A股数据采集框架 - 使用示例")
    print("=" * 50)
    
    try:
        # 运行示例
        example_basic_data_acquisition()
        example_multiple_stocks()
        example_backtesting_interface()
        example_data_validation()
        example_caching()
        example_configuration()
        example_stock_info()
        example_market_analysis()
        
        print("\n" + "=" * 50)
        print("所有示例运行完成！")
        
    except Exception as e:
        print(f"\n运行示例时出错: {e}")
        import traceback
        traceback.print_exc()
