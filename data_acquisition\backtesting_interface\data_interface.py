"""
Clean interface for backtesting systems to access A-Share data
"""

import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime, date, timedelta
from ..core.data_manager import DataManager
from ..utils.stock_codes import normalize_stock_code, validate_stock_codes
from ..config.settings import Config
from ..utils.logger import get_logger

class BacktestingDataInterface:
    """Clean interface for backtesting systems"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize backtesting data interface
        
        Args:
            config: Configuration instance
        """
        self.config = config or Config()
        self.logger = get_logger('backtesting_interface', config)
        
        # Initialize data manager
        self.data_manager = DataManager(config)
        
        self.logger.info("Backtesting data interface initialized")
    
    def get_stock_data(self,
                      stock_code: str,
                      start_date: Union[str, date, datetime],
                      end_date: Union[str, date, datetime],
                      fields: Optional[List[str]] = None) -> Optional[pd.DataFrame]:
        """
        Get stock data for backtesting
        
        Args:
            stock_code: Stock code (e.g., '000001.SZ')
            start_date: Start date
            end_date: End date
            fields: Specific fields to return (None for all)
            
        Returns:
            pd.DataFrame: Stock data with DatetimeIndex
        """
        try:
            data = self.data_manager.get_stock_data(stock_code, start_date, end_date)
            
            if data is None or data.empty:
                return None
            
            # Filter fields if specified
            if fields:
                available_fields = [field for field in fields if field in data.columns]
                if available_fields:
                    data = data[available_fields]
                else:
                    self.logger.warning(f"None of the requested fields {fields} are available")
                    return None
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error getting stock data for backtesting: {e}")
            return None
    
    def get_multiple_stocks_data(self,
                               stock_codes: List[str],
                               start_date: Union[str, date, datetime],
                               end_date: Union[str, date, datetime],
                               fields: Optional[List[str]] = None) -> Dict[str, pd.DataFrame]:
        """
        Get data for multiple stocks for backtesting
        
        Args:
            stock_codes: List of stock codes
            start_date: Start date
            end_date: End date
            fields: Specific fields to return
            
        Returns:
            Dict[str, pd.DataFrame]: Dictionary of stock data
        """
        try:
            # Get data for all stocks
            all_data = self.data_manager.get_multiple_stocks_data(
                stock_codes, start_date, end_date
            )
            
            # Filter fields if specified
            if fields:
                filtered_data = {}
                for stock_code, data in all_data.items():
                    if data is not None and not data.empty:
                        available_fields = [field for field in fields if field in data.columns]
                        if available_fields:
                            filtered_data[stock_code] = data[available_fields]
                return filtered_data
            
            return all_data
            
        except Exception as e:
            self.logger.error(f"Error getting multiple stocks data: {e}")
            return {}
    
    def get_universe_data(self,
                         universe: List[str],
                         start_date: Union[str, date, datetime],
                         end_date: Union[str, date, datetime],
                         fields: List[str] = ['open', 'high', 'low', 'close', 'volume']) -> pd.DataFrame:
        """
        Get aligned data for a universe of stocks
        
        Args:
            universe: List of stock codes
            start_date: Start date
            end_date: End date
            fields: Fields to include
            
        Returns:
            pd.DataFrame: Multi-level DataFrame with (date, stock_code) index
        """
        try:
            # Get data for all stocks
            all_data = self.get_multiple_stocks_data(universe, start_date, end_date, fields)
            
            if not all_data:
                return pd.DataFrame()
            
            # Create aligned DataFrame
            aligned_data = []
            
            for stock_code, data in all_data.items():
                if data is not None and not data.empty:
                    # Add stock_code as a column
                    data_copy = data.copy()
                    data_copy['stock_code'] = stock_code
                    aligned_data.append(data_copy)
            
            if not aligned_data:
                return pd.DataFrame()
            
            # Concatenate all data
            combined_data = pd.concat(aligned_data)
            
            # Create multi-index
            combined_data = combined_data.reset_index()
            combined_data = combined_data.set_index(['date', 'stock_code'])
            
            return combined_data
            
        except Exception as e:
            self.logger.error(f"Error getting universe data: {e}")
            return pd.DataFrame()
    
    def get_price_matrix(self,
                        stock_codes: List[str],
                        start_date: Union[str, date, datetime],
                        end_date: Union[str, date, datetime],
                        price_type: str = 'close') -> pd.DataFrame:
        """
        Get price matrix for multiple stocks (dates as rows, stocks as columns)
        
        Args:
            stock_codes: List of stock codes
            start_date: Start date
            end_date: End date
            price_type: Type of price ('open', 'high', 'low', 'close')
            
        Returns:
            pd.DataFrame: Price matrix with dates as index and stocks as columns
        """
        try:
            # Get data for all stocks
            all_data = self.get_multiple_stocks_data(
                stock_codes, start_date, end_date, [price_type]
            )
            
            if not all_data:
                return pd.DataFrame()
            
            # Create price matrix
            price_data = {}
            for stock_code, data in all_data.items():
                if data is not None and not data.empty and price_type in data.columns:
                    price_data[stock_code] = data[price_type]
            
            if not price_data:
                return pd.DataFrame()
            
            # Combine into matrix
            price_matrix = pd.DataFrame(price_data)
            
            # Forward fill missing values (up to 3 days)
            price_matrix = price_matrix.fillna(method='ffill', limit=3)
            
            return price_matrix
            
        except Exception as e:
            self.logger.error(f"Error creating price matrix: {e}")
            return pd.DataFrame()
    
    def get_returns_matrix(self,
                          stock_codes: List[str],
                          start_date: Union[str, date, datetime],
                          end_date: Union[str, date, datetime],
                          return_type: str = 'simple') -> pd.DataFrame:
        """
        Get returns matrix for multiple stocks
        
        Args:
            stock_codes: List of stock codes
            start_date: Start date
            end_date: End date
            return_type: Type of returns ('simple', 'log')
            
        Returns:
            pd.DataFrame: Returns matrix
        """
        try:
            # Get price matrix
            price_matrix = self.get_price_matrix(stock_codes, start_date, end_date, 'close')
            
            if price_matrix.empty:
                return pd.DataFrame()
            
            # Calculate returns
            if return_type == 'simple':
                returns = price_matrix.pct_change()
            elif return_type == 'log':
                returns = np.log(price_matrix / price_matrix.shift(1))
            else:
                raise ValueError(f"Unknown return type: {return_type}")
            
            # Remove first row (NaN values)
            returns = returns.dropna(how='all')
            
            return returns
            
        except Exception as e:
            self.logger.error(f"Error calculating returns matrix: {e}")
            return pd.DataFrame()
    
    def get_market_data(self,
                       start_date: Union[str, date, datetime],
                       end_date: Union[str, date, datetime],
                       indices: List[str] = ['sh000001', 'sz399001']) -> Dict[str, pd.DataFrame]:
        """
        Get market index data
        
        Args:
            start_date: Start date
            end_date: End date
            indices: List of index codes
            
        Returns:
            Dict[str, pd.DataFrame]: Dictionary of index data
        """
        try:
            market_data = {}
            
            for index_code in indices:
                data = self.data_manager.get_index_data(index_code, start_date, end_date)
                if data is not None and not data.empty:
                    market_data[index_code] = data
            
            return market_data
            
        except Exception as e:
            self.logger.error(f"Error getting market data: {e}")
            return {}
    
    def get_stock_info_batch(self, stock_codes: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Get stock information for multiple stocks
        
        Args:
            stock_codes: List of stock codes
            
        Returns:
            Dict[str, Dict]: Dictionary of stock information
        """
        try:
            stock_info = {}
            
            for stock_code in stock_codes:
                info = self.data_manager.get_stock_info(stock_code)
                if info:
                    stock_info[stock_code] = info
            
            return stock_info
            
        except Exception as e:
            self.logger.error(f"Error getting stock info batch: {e}")
            return {}
    
    def get_trading_calendar(self,
                           start_date: Union[str, date, datetime],
                           end_date: Union[str, date, datetime]) -> List[date]:
        """
        Get trading calendar (business days excluding holidays)
        
        Args:
            start_date: Start date
            end_date: End date
            
        Returns:
            List[date]: List of trading dates
        """
        try:
            # Convert to datetime
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date).date()
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date).date()
            
            # Generate business days (this is a simplified version)
            # In practice, you would want to exclude Chinese holidays
            business_days = pd.bdate_range(start=start_date, end=end_date)
            
            return [d.date() for d in business_days]
            
        except Exception as e:
            self.logger.error(f"Error getting trading calendar: {e}")
            return []
    
    def get_data_availability(self, stock_codes: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Get data availability information for stocks
        
        Args:
            stock_codes: List of stock codes
            
        Returns:
            Dict[str, Dict]: Data availability information
        """
        try:
            availability = {}
            
            for stock_code in stock_codes:
                coverage = self.data_manager.database.get_data_coverage(stock_code)
                availability[stock_code] = coverage
            
            return availability
            
        except Exception as e:
            self.logger.error(f"Error getting data availability: {e}")
            return {}
    
    def validate_universe(self, stock_codes: List[str]) -> Tuple[List[str], List[str]]:
        """
        Validate a universe of stock codes
        
        Args:
            stock_codes: List of stock codes to validate
            
        Returns:
            Tuple[List[str], List[str]]: (valid_codes, invalid_codes)
        """
        return validate_stock_codes(stock_codes)
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            self.data_manager.cleanup()
            self.logger.info("Backtesting interface cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
